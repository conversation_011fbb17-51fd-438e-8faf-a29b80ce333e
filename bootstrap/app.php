<?php

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command('queue:remove-inactive-players')->everyMinute();
        $schedule->command('telescope:prune')->daily();
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\EnsureAnonymousUserIdentity::class,
//            \App\Http\Middleware\ExtendSessionLifetime::class,
//            \App\Http\Middleware\HandleRefreshToken::class,
        ]);

        // Exclude broadcasting auth from CSRF protection
        $middleware->validateCsrfTokens(except: [
            'broadcasting/auth',
            'test-broadcasting-auth',
        ]);

        // Register custom authentication middlewares
        $middleware->alias([
            'machine.auth' => \App\Http\Middleware\MachineAuthentication::class,
            'admin.auth' => \App\Http\Middleware\AdminAuthentication::class,
            'anonymous.identity' => \App\Http\Middleware\EnsureAnonymousUserIdentity::class,
            'broadcasting.auth' => \App\Http\Middleware\BroadcastingAuthMiddleware::class,
        ]);
        $middleware->trustProxies(at: '*', headers: Request::HEADER_X_FORWARDED_FOR |
            Request::HEADER_X_FORWARDED_HOST |
            Request::HEADER_X_FORWARDED_PORT |
            Request::HEADER_X_FORWARDED_PROTO |
            Request::HEADER_X_FORWARDED_AWS_ELB
        );

        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
