<?php

use App\Services\AnonymousUserService;
use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    // Only authenticated users can access their private channel
    return $user && (int)$user->id === (int)$id;
});

Broadcast::channel('machineControl.{clawUserId}', function ($user, $clawUserId) {
    if ($user) {
        // Authenticated user
        return [
            'userId' => $user->id,
            'name' => $user->name,
            'anon' => false,
        ];
    } else {
        // Anonymous users are not allowed in machine control channels
        return false;
    }
});

Broadcast::channel('machineSpectator.{clawUserId}', function ($user, $clawUserId) {
    if ($user && !isset($user->anonymous)) {
        // Authenticated user
        return [
            'userId' => $user->id,
            'name' => $user->name,
            'anon' => false,
        ];
    } else {
        // Anonymous user - get their data from the service
        $anonymousService = app(AnonymousUserService::class);
        $anonymousUser = $anonymousService->getAnonymousUserFromSession();

        if ($anonymousUser) {
            return $anonymousUser;
        }

        // If no anonymous user data, allow but with minimal info
        return [
            'userId' => 'anon_' . substr(session()->getId(), 0, 8),
            'name' => 'Espectador Anónimo',
            'anon' => true,
        ];
    }
});

Broadcast::channel('publicity', function ($user, $clawUserId) {
    // Public channel - always allow
    return true;
});
