<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::prefix('v1/webhooks')->name('webhooks.')->group(function () {
    Route::post('mercardo-pago',[\App\Http\Controllers\api\MercadoPagoController::class,'webhook'])->name('mercado-pago');
});

// Machine API routes
Route::get('machine/{machineId}/controller-status', [\App\Http\Controllers\MainController::class, 'getControllerStatus'])
    ->name('machine.controller-status');

// SkyDropX API routes
Route::prefix('v1/skydropx')->name('skydropx.')->group(function () {
    Route::get('test-connection', [\App\Http\Controllers\SkyDropXController::class, 'testConnection'])->name('test-connection');
    Route::post('shipping-rates', [\App\Http\Controllers\SkyDropXController::class, 'getShippingRates'])->name('shipping-rates');
    Route::post('shipments', [\App\Http\Controllers\SkyDropXController::class, 'createShipment'])->name('shipments.create');
    Route::get('shipments/{shipmentId}', [\App\Http\Controllers\SkyDropXController::class, 'getShipment'])->name('shipments.show');
    Route::get('track/{trackingNumber}', [\App\Http\Controllers\SkyDropXController::class, 'trackShipment'])->name('track');
    Route::get('carriers', [\App\Http\Controllers\SkyDropXController::class, 'getCarriers'])->name('carriers');
    Route::post('validate-address', [\App\Http\Controllers\SkyDropXController::class, 'validateAddress'])->name('validate-address');
});
