<?php

namespace App\Services;

use App\DTOs\SkyDropX\AccessTokenDTO;
use App\DTOs\SkyDropX\AddressDTO;
use App\DTOs\SkyDropX\PackageDTO;
use App\DTOs\SkyDropX\ShipmentDTO;
use App\Exceptions\SkyDropXApiException;
use App\Exceptions\SkyDropXAuthException;
use App\Exceptions\SkyDropXException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SkyDropXService
{
    private Client $httpClient;
    private string $baseUrl;
    private string $clientId;
    private string $clientSecret;
    private int $timeout;
    private bool $sandbox;
    private ?AccessTokenDTO $accessToken = null;

    public function __construct()
    {
        $config = config('services.skydropx');

        $this->baseUrl = $config['base_url'];
        $this->clientId = $config['client_id'];
        $this->clientSecret = $config['client_secret'];
        $this->timeout = $config['timeout'];
        $this->sandbox = $config['sandbox'];

        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'Laravel-SkyDropX-Client/1.0',
            ],
        ]);

        $this->validateConfiguration();
    }

    /**
     * Validate that all required configuration is present
     */
    private function validateConfiguration(): void
    {
        if (empty($this->clientId) || empty($this->clientSecret)) {
            throw new SkyDropXException('SkyDropX client ID and client secret are required');
        }

        if (empty($this->baseUrl)) {
            throw new SkyDropXException('SkyDropX base URL is required');
        }
    }

    /**
     * Get access token using client_credentials OAuth flow
     */
    public function getAccessToken(bool $forceRefresh = false): AccessTokenDTO
    {
        $cacheKey = 'skydropx_access_token';

        // Return cached token if valid and not forcing refresh
        if (!$forceRefresh && $this->accessToken && !$this->accessToken->isExpired()) {
            return $this->accessToken;
        }

        // Try to get from cache
        if (!$forceRefresh) {
            $cachedToken = Cache::get($cacheKey);
            if ($cachedToken && $cachedToken instanceof AccessTokenDTO && !$cachedToken->isExpired()) {
                $this->accessToken = $cachedToken;
                return $this->accessToken;
            }
        }

        try {
            Log::info('Requesting new SkyDropX access token');

            $response = $this->httpClient->post('/api/v1/oauth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                ],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['access_token'])) {
                throw new SkyDropXAuthException('Invalid response from OAuth endpoint');
            }

            $this->accessToken = AccessTokenDTO::fromArray($data);

            // Cache the token for 90% of its lifetime
            $cacheSeconds = (int) ($this->accessToken->expiresIn * 0.9);
            Cache::put($cacheKey, $this->accessToken, $cacheSeconds);

            Log::info('SkyDropX access token obtained successfully', [
                'expires_in' => $this->accessToken->expiresIn,
                'token_type' => $this->accessToken->tokenType,
            ]);

            return $this->accessToken;

        } catch (ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            $responseBody = $e->getResponse()->getBody()->getContents();

            Log::error('SkyDropX OAuth authentication failed', [
                'status_code' => $statusCode,
                'response' => $responseBody,
            ]);

            throw new SkyDropXAuthException(
                'Failed to authenticate with SkyDropX: ' . $responseBody,
                $statusCode,
                json_decode($responseBody, true) ?? []
            );

        } catch (GuzzleException $e) {
            Log::error('SkyDropX OAuth request failed', [
                'error' => $e->getMessage(),
            ]);

            throw new SkyDropXAuthException('OAuth request failed: ' . $e->getMessage());
        }
    }

    /**
     * Make an authenticated API request
     */
    private function makeAuthenticatedRequest(string $method, string $endpoint, array $options = []): array
    {
        $token = $this->getAccessToken();

        $options['headers'] = array_merge(
            $options['headers'] ?? [],
            ['Authorization' => $token->getAuthorizationHeader()]
        );

        try {
            $response = $this->httpClient->request($method, $endpoint, $options);
            $data = json_decode($response->getBody()->getContents(), true);

            Log::debug('SkyDropX API request successful', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $response->getStatusCode(),
            ]);

            return $data ?? [];

        } catch (ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            $responseBody = $e->getResponse()->getBody()->getContents();

            // If unauthorized, try to refresh token once
            if ($statusCode === 401 && !($options['_token_refreshed'] ?? false)) {
                Log::info('Token expired, refreshing and retrying request');

                $this->getAccessToken(true); // Force refresh
                $options['_token_refreshed'] = true;

                return $this->makeAuthenticatedRequest($method, $endpoint, $options);
            }

            Log::error('SkyDropX API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $statusCode,
                'response' => $responseBody,
            ]);

            throw new SkyDropXApiException(
                "API request failed: {$responseBody}",
                $statusCode,
                json_decode($responseBody, true) ?? []
            );

        } catch (ServerException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            $responseBody = $e->getResponse()->getBody()->getContents();

            Log::error('SkyDropX server error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $statusCode,
                'response' => $responseBody,
            ]);

            throw new SkyDropXApiException(
                "Server error: {$responseBody}",
                $statusCode,
                json_decode($responseBody, true) ?? []
            );

        } catch (GuzzleException $e) {
            Log::error('SkyDropX request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
            ]);

            throw new SkyDropXApiException('Request failed: ' . $e->getMessage());
        }
    }

    /**
     * Create a new shipment
     */
    public function createShipment(
        AddressDTO $origin,
        AddressDTO $destination,
        PackageDTO $package,
        array $options = []
    ): ShipmentDTO {
        $payload = [
            'origin' => $origin->toArray(),
            'destination' => $destination->toArray(),
            'package' => $package->toArray(),
        ];

        // Add optional parameters
        if (!empty($options)) {
            $payload = array_merge($payload, $options);
        }

        Log::info('Creating SkyDropX shipment', [
            'origin_city' => $origin->city,
            'destination_city' => $destination->city,
            'package_weight' => $package->weight,
        ]);

        $response = $this->makeAuthenticatedRequest('POST', '/v1/shipments', [
            'json' => $payload,
        ]);

        return ShipmentDTO::fromArray($response['data'] ?? $response);
    }

    /**
     * Get shipment by ID
     */
    public function getShipment(int $shipmentId): ShipmentDTO
    {
        Log::info('Retrieving SkyDropX shipment', ['shipment_id' => $shipmentId]);

        $response = $this->makeAuthenticatedRequest('GET', "/v1/shipments/{$shipmentId}");

        return ShipmentDTO::fromArray($response['data'] ?? $response);
    }

    /**
     * Track a shipment by tracking number
     */
    public function trackShipment(string $trackingNumber): array
    {
        Log::info('Tracking SkyDropX shipment', ['tracking_number' => $trackingNumber]);

        $response = $this->makeAuthenticatedRequest('GET', "/v1/shipments/track/{$trackingNumber}");

        return $response['data'] ?? $response;
    }

    /**
     * Get shipping rates
     */
    public function getShippingRates(
        AddressDTO $origin,
        AddressDTO $destination,
        PackageDTO $package
    ): array {
        $payload = [
            'origin' => $origin->toArray(),
            'destination' => $destination->toArray(),
            'package' => $package->toArray(),
        ];

        Log::info('Getting SkyDropX shipping rates', [
            'origin_city' => $origin->city,
            'destination_city' => $destination->city,
        ]);

        $response = $this->makeAuthenticatedRequest('POST', '/v1/rates', [
            'json' => $payload,
        ]);

        return $response['data'] ?? $response;
    }

    /**
     * Cancel a shipment
     */
    public function cancelShipment(int $shipmentId): bool
    {
        Log::info('Cancelling SkyDropX shipment', ['shipment_id' => $shipmentId]);

        try {
            $this->makeAuthenticatedRequest('DELETE', "/v1/shipments/{$shipmentId}");
            return true;
        } catch (SkyDropXApiException $e) {
            Log::error('Failed to cancel SkyDropX shipment', [
                'shipment_id' => $shipmentId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get available carriers
     */
    public function getOrders(): array
    {
        Log::info('Retrieving SkyDropX orders');

        $response = $this->makeAuthenticatedRequest('GET', '/api/v1/orders');

        return ($response['properties'] ?? $response)['data'] ?? $response;
    }

    public function createQuotation(AddressDTO $origin, AddressDTO $destination, PackageDTO $package): array
    {
        Log::info('Creating quotation with SkyDropX carriers');

        $response = $this->makeAuthenticatedRequest('POST', '/api/v1/quotations', [
            'json' => [
                'address_from' => $origin->toArray(),
                'address_to' => $destination->toArray(),
                'parcel' => $package->toArray(),
            ],
        ]);

        return ($response['properties'] ?? $response)['data'] ?? $response;
    }

    /**
     * Validate an address
     */
    public function validateAddress(AddressDTO $address): array
    {
        Log::info('Validating address with SkyDropX', [
            'city' => $address->city,
            'postal_code' => $address->postalCode,
        ]);

        $response = $this->makeAuthenticatedRequest('POST', '/v1/addresses/validate', [
            'json' => $address->toArray(),
        ]);

        return $response['data'] ?? $response;
    }

    /**
     * Get service status
     */
    public function getServiceStatus(): array
    {
        try {
            $response = $this->makeAuthenticatedRequest('GET', '/v1/status');
            return $response;
        } catch (SkyDropXException $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Test the connection and authentication
     */
    public function testConnection(): array
    {
        try {
            $token = $this->getAccessToken();

            return [
                'success' => true,
                'message' => 'Connection successful',
                'token_type' => $token->tokenType,
                'expires_in' => $token->expiresIn,
                'sandbox' => $this->sandbox,
            ];
        } catch (SkyDropXException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
        }
    }
}
