<?php

namespace App\Console\Commands;

use App\Services\SkyDropXService;
use Illuminate\Console\Command;

class TestSkyDropXConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'skydropx:test-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the connection to SkyDropX API';

    /**
     * Execute the console command.
     */
    public function handle(SkyDropXService $skyDropXService)
    {
        $this->info('Testing SkyDropX API connection...');
        $this->newLine();

        try {
            $result = $skyDropXService->testConnection();

            if ($result['success']) {
                $this->info('✅ Connection successful!');
                $this->table(
                    ['Property', 'Value'],
                    [
                        ['Token Type', $result['token_type']],
                        ['Expires In', $result['expires_in'] . ' seconds'],
                        ['Sandbox Mode', $result['sandbox'] ? 'Yes' : 'No'],
                    ]
                );
            } else {
                $this->error('❌ Connection failed!');
                $this->error('Message: ' . $result['message']);
                if (isset($result['error_code'])) {
                    $this->error('Error Code: ' . $result['error_code']);
                }
                return Command::FAILURE;
            }

            // Test getting carriers
            $this->newLine();
            $this->info('Testing carriers endpoint...');

            try {
                $orders = $skyDropXService->getOrders();
                $this->info('✅ Orders retrieved successfully!');
                $this->info('Found ' . json_encode($orders) . ' orders');
            } catch (\Exception $e) {
                $this->warn('⚠️  Could not retrieve orders: ' . $e->getMessage());
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Connection test failed!');
            $this->error('Error: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
