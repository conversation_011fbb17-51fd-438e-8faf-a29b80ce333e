<?php

namespace App\DTOs\SkyDropX;

class AddressDTO
{
    public function __construct(
        public readonly string $street,
        public readonly string $city,
        public readonly string $state,
        public readonly string $postalCode,
        public readonly string $country = 'MX',
        public readonly string $neighborhood,
        public readonly ?string $company = null,
        public readonly ?string $phone = null,
        public readonly ?string $email = null,
        public readonly ?string $reference = null
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            street: $data['street'],
            city: $data['area_level2'],
            state: $data['area_level1'],
            postalCode: $data['postal_code'],
            country: $data['country_code'],
            neighborhood: $data['area_level3'] ?? null,
            company: $data['company'] ?? null,
            phone: $data['phone'] ?? null,
            email: $data['email'] ?? null,
            reference: $data['reference'] ?? null
        );
    }

    public function toArray(): array
    {
        return array_filter([
            'company' => $this->company,
            'street' => $this->street,
            'area_level3' => $this->neighborhood,
            'area_level2' => $this->city,
            'area_level1' => $this->state,
            'postal_code' => $this->postalCode,
            'country_code' => $this->country,
            'phone' => $this->phone,
            'email' => $this->email,
            'reference' => $this->reference,
        ], fn($value) => $value !== null);
    }
}
