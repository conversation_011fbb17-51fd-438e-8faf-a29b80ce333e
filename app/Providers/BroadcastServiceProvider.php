<?php

namespace App\Providers;

use App\Services\AnonymousUserService;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Don't register routes here - they're already registered by Laravel
        // We'll override the controller binding instead

        // Override the broadcasting controller
        $this->app->bind(
            \Illuminate\Broadcasting\BroadcastController::class,
            \App\Http\Controllers\BroadcastingController::class
        );
    }

    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }
}
