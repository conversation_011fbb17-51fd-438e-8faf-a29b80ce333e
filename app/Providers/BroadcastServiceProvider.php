<?php

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register broadcasting routes with custom middleware
        Broadcast::routes([
            'middleware' => ['web', 'broadcasting.auth'],
        ]);

        // Override the user resolver for broadcasting to support anonymous users
        Broadcast::channel('*', function ($user, ...$parameters) {
            // This will be overridden by specific channel definitions
            return true;
        });

        require base_path('routes/channels.php');
    }

    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Override the default broadcasting controller
        $this->app->bind(
            \Illuminate\Broadcasting\BroadcastController::class,
            \App\Http\Controllers\BroadcastingController::class
        );
    }
}
