<?php

namespace App\Http\Controllers;

use App\Services\AnonymousUserService;
use Illuminate\Broadcasting\BroadcastController as BaseBroadcastController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

class BroadcastingController extends BaseBroadcastController
{
    protected AnonymousUserService $anonymousUserService;

    public function __construct(AnonymousUserService $anonymousUserService)
    {
        $this->anonymousUserService = $anonymousUserService;
        parent::__construct();
    }

    /**
     * Authenticate the request for channel access with anonymous user support
     */
    public function authenticate(Request $request)
    {
        try {
            // Log the incoming request for debugging
            Log::info('Broadcasting authentication request', [
                'method' => $request->method(),
                'url' => $request->url(),
                'input' => $request->all(),
                'session_id' => $request->session()->getId(),
                'user_authenticated' => auth()->check(),
            ]);

            // Ensure anonymous user identity is created if not authenticated
            if (!Auth::check()) {
                $this->anonymousUserService->getOrCreateAnonymousUser($request);
            }

            // Call the parent authenticate method but with our custom user resolution
            return $this->authenticateWithAnonymousSupport($request);

        } catch (\Exception $e) {
            Log::error('Broadcasting authentication error', [
                'error' => $e->getMessage(),
                'channel' => $request->input('channel_name'),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json(['error' => 'Authentication failed: ' . $e->getMessage()], 403);
        }
    }

    /**
     * Custom authentication that supports anonymous users
     */
    protected function authenticateWithAnonymousSupport(Request $request)
    {
        // Temporarily override the user resolver for this request
        $originalResolver = Auth::userResolver();

        Auth::setUserResolver(function () use ($request) {
            $user = $originalResolver();

            if ($user) {
                return $user;
            }

            // For anonymous users, return a fake user object that channels can detect
            return new class {
                public $id = null;
                public $name = 'Anonymous';
                public $anonymous = true;
            };
        });

        try {
            // Call Laravel's built-in authentication
            $response = parent::authenticate($request);

            // Restore the original resolver
            Auth::setUserResolver($originalResolver);

            return $response;

        } catch (\Exception $e) {
            // Restore the original resolver
            Auth::setUserResolver($originalResolver);
            throw $e;
        }
    }

}
