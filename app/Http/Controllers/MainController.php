<?php

namespace App\Http\Controllers;

use App\Models\Banner;
use App\Models\Category;
use App\Models\MachineInfo;
use App\Models\Prize;
use App\Models\User;
use App\Services\GameService;
use App\Services\QueueService;
use App\Services\WalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

const CACHE_REVALIDATION_RULES = [1,2];
class MainController extends Controller
{
    protected $gameService;
    protected $walletService;
    protected $queueService;

    public function __construct(GameService $gameService, WalletService $walletService, QueueService $queueService)
    {
        $this->gameService = $gameService;
        $this->walletService = $walletService;
        $this->queueService = $queueService;
    }
    function home(?Category $category): \Inertia\Response|\Inertia\ResponseFactory
    {
//        $prizesCacheKey = $category == null ? 'prizes' : 'prizes-' . $category->id;
        $machines = MachineInfo::query()
            ->inCategory($category?->id)
            ->with('prize')
            ->get();
        $banners = Cache::flexible('banners-list',CACHE_REVALIDATION_RULES,function () {
            return Banner::query()->get();
        });
        $categories = Cache::flexible('category-list', CACHE_REVALIDATION_RULES, function () {
            return Category::query()->get();
        });
        return inertia('Dashboard', compact('machines', 'banners', 'categories'));
    }

    function watchGame(string $machineId)
    {
        $channelId = $machineId;
        $machine = MachineInfo::query()->where(['uuid' => $machineId])->with('prize')->first();
        $queueInfo = $this->queueService->getQueueInfo($machineId);
        $userPosition = null;

        // Get user balance if authenticated
        $userBalance = 0;
        $hasEnoughPoints = false;
        $isAuthenticated = Auth::check();

        if ($isAuthenticated) {
            $user = Auth::user();
            $userPosition = $this->queueService->getQueuePosition($user, $machineId);

            // Check if user has enough points
            $userBalance = $this->walletService->getUserBalance($user);
            $hasEnoughPoints = $machine && $machine->prize && $userBalance >= $machine->prize->price;
        }

        return inertia('Game/MachineSpectator', compact(
            'channelId',
            'machine',
            'queueInfo',
            'userPosition',
            'userBalance',
            'hasEnoughPoints',
            'isAuthenticated'
        ));
    }

    function playGame(string $machineId)
    {
        $channelId = $machineId;
        $machine = MachineInfo::query()->where(['uuid' => $machineId])->with('prize')->first();

        // Check if user is authenticated
        $canPlay = false;
        $message = '';
        $userBalance = 0;
        $queueInfo = null;
        $userPosition = null;

        if (Auth::check()) {
            $user = Auth::user();
            $userBalance = $this->walletService->getUserBalance($user);

            // Check if user can play (is current player in queue)
            $canPlayResult = $this->queueService->canUserPlay($user, $machineId);
            $canPlay = $canPlayResult['can_play'];
            $message = $canPlayResult['message'];

            // Log the result for debugging
            Log::info('Play game request', [
                'user_id' => $user->id,
                'machine_id' => $machineId,
                'can_play' => $canPlay,
                'message' => $message,
                'already_charged' => isset($canPlayResult['already_charged']) ? $canPlayResult['already_charged'] : false,
                'just_charged' => isset($canPlayResult['just_charged']) ? $canPlayResult['just_charged'] : false,
                'is_current_player' => isset($canPlayResult['is_current_player']) ? $canPlayResult['is_current_player'] : false
            ]);

            // Get queue information
            $queueInfo = $this->queueService->getQueueInfo($machineId);
            $userPosition = $this->queueService->getQueuePosition($user, $machineId);

            // If user is not in queue, redirect to queue page
            if (!$userPosition['in_queue']) {
                return redirect()->route('machine.queue', $machineId);
            }

            // If the player's time has expired, redirect to queue page
            if (isset($canPlayResult['redirect_to_queue']) && $canPlayResult['redirect_to_queue']) {
                return redirect()->route('machine.queue', $machineId)->with('error', $message);
            }

            // If user is in queue but not current player, redirect to queue page
            if (!isset($canPlayResult['is_current_player']) || !$canPlayResult['is_current_player']) {
                return redirect()->route('machine.queue', $machineId);
            }

            // Update user balance after payment (if just charged)
            if (isset($canPlayResult['just_charged']) && $canPlayResult['just_charged']) {
                $userBalance = $this->walletService->getUserBalance($user);

                // Store a session flag to prevent double charging on refresh
                session(['game_' . $machineId . '_paid' => true]);

                Log::info('Player charged for game', [
                    'user_id' => $user->id,
                    'machine_id' => $machineId,
                    'new_balance' => $userBalance
                ]);
            } else if (session('game_' . $machineId . '_paid')) {
                // If we have a session flag, the player has already been charged
                $canPlay = true;
                $message = 'You are already playing';

                Log::info('Using session flag to prevent double charging', [
                    'user_id' => $user->id,
                    'machine_id' => $machineId
                ]);
            }
        } else {
            $message = __('You need to be logged in to play');
            return redirect()->route('login');
        }

        return inertia('Game/MachineGame', compact(
            'channelId',
            'machine',
            'canPlay',
            'message',
            'userBalance',
            'queueInfo',
            'userPosition'
        ));
    }

    function controlGame(string $machineId)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // Redirect to the standard login page with a return URL
            return redirect()->route('login')
                ->with('intended', route('machine.control', $machineId));
        }

        /**
         * @var User $user
         */
        $user = Auth::user();

        // Check if user has MACHINE role and is authorized to control this machine
        if (!$user->hasRole(User::ROLE_MACHINE) || !$user->canControlMachine($machineId)) {
            // If not authorized, log the attempt and redirect to home
            Log::warning('Unauthorized access attempt to machine control page', [
                'user_id' => $user->id,
                'machine_id' => $machineId,
                'ip' => request()->ip()
            ]);

            return redirect()->route('home')
                ->with('error', __('You are not authorized to control this machine'));
        }

        $channelId = $machineId;
        $machine = MachineInfo::query()->where(['uuid' => $machineId])->with('prize')->first();

        return inertia('Game/MachineController', compact('channelId', 'machine'));
    }

    /**
     * Get the controller status for a machine
     */
    public function getControllerStatus(string $machineId)
    {
        // Check if there's a controller connected for this machine
        // This could be implemented by checking:
        // 1. If there's an active session for a machine controller user
        // 2. If there's recent activity in the presence channel
        // 3. If there's a heartbeat from the controller

        // For now, we'll check if there's a user with MACHINE role that can control this machine
        $machine = MachineInfo::where('uuid', $machineId)->first();

        if (!$machine) {
            return response()->json([
                'connected' => false,
                'message' => 'Machine not found'
            ], 404);
        }

        // Check if there are any users with MACHINE role that can control this machine
        $controllerUsers = User::whereHas('roles', function ($query) {
            $query->where('name', User::ROLE_MACHINE);
        })->get();

        $hasAuthorizedController = false;
        foreach ($controllerUsers as $user) {
            if ($user->canControlMachine($machineId)) {
                $hasAuthorizedController = true;
                break;
            }
        }

        // In a real implementation, you might want to check:
        // - Last activity timestamp
        // - WebSocket connection status
        // - Presence channel members

        return response()->json([
            'connected' => $hasAuthorizedController,
            'machine_id' => $machineId,
            'machine_name' => $machine->prize->title ?? 'Unknown Machine',
            'status' => $hasAuthorizedController ? 'online' : 'offline',
            'last_seen' => now()->toISOString(), // In real implementation, use actual last seen
        ]);
    }
}
