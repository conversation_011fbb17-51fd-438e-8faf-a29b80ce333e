<?php

namespace App\Http\Controllers;

use App\DTOs\SkyDropX\AddressDTO;
use App\DTOs\SkyDropX\PackageDTO;
use App\Exceptions\SkyDropXException;
use App\Services\SkyDropXService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SkyDropXController extends Controller
{
    public function __construct(
        private SkyDropXService $skyDropXService
    ) {}

    /**
     * Test the SkyDropX connection
     */
    public function testConnection(): JsonResponse
    {
        try {
            $result = $this->skyDropXService->testConnection();

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (SkyDropXException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ], 500);
        }
    }

    /**
     * Get shipping rates
     */
    public function getShippingRates(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'origin' => 'required|array',
            'origin.name' => 'required|string',
            'origin.street' => 'required|string',
            'origin.city' => 'required|string',
            'origin.state' => 'required|string',
            'origin.postal_code' => 'required|string',
            'origin.country' => 'required|string',

            'destination' => 'required|array',
            'destination.name' => 'required|string',
            'destination.street' => 'required|string',
            'destination.city' => 'required|string',
            'destination.state' => 'required|string',
            'destination.postal_code' => 'required|string',
            'destination.country' => 'required|string',

            'package' => 'required|array',
            'package.weight' => 'required|numeric|min:0.1',
            'package.length' => 'required|numeric|min:1',
            'package.width' => 'required|numeric|min:1',
            'package.height' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $origin = AddressDTO::fromArray($request->input('origin'));
            $destination = AddressDTO::fromArray($request->input('destination'));
            $package = PackageDTO::fromArray($request->input('package'));

            $rates = $this->skyDropXService->getShippingRates($origin, $destination, $package);

            return response()->json([
                'success' => true,
                'data' => $rates,
            ]);
        } catch (SkyDropXException $e) {
            Log::error('Failed to get shipping rates', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a shipment
     */
    public function createShipment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'origin' => 'required|array',
            'destination' => 'required|array',
            'package' => 'required|array',
            'options' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $origin = AddressDTO::fromArray($request->input('origin'));
            $destination = AddressDTO::fromArray($request->input('destination'));
            $package = PackageDTO::fromArray($request->input('package'));
            $options = $request->input('options', []);

            $shipment = $this->skyDropXService->createShipment($origin, $destination, $package, $options);

            return response()->json([
                'success' => true,
                'data' => $shipment->toArray(),
            ]);
        } catch (SkyDropXException $e) {
            Log::error('Failed to create shipment', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shipment details
     */
    public function getShipment(int $shipmentId): JsonResponse
    {
        try {
            $shipment = $this->skyDropXService->getShipment($shipmentId);

            return response()->json([
                'success' => true,
                'data' => $shipment->toArray(),
            ]);
        } catch (SkyDropXException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Track a shipment
     */
    public function trackShipment(string $trackingNumber): JsonResponse
    {
        try {
            $tracking = $this->skyDropXService->trackShipment($trackingNumber);

            return response()->json([
                'success' => true,
                'data' => $tracking,
            ]);
        } catch (SkyDropXException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available carriers
     */
    public function getCarriers(): JsonResponse
    {
        try {
            $carriers = $this->skyDropXService->getOrders();

            return response()->json([
                'success' => true,
                'data' => $carriers,
            ]);
        } catch (SkyDropXException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate an address
     */
    public function validateAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'street' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'postal_code' => 'required|string',
            'country' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $address = AddressDTO::fromArray($request->all());
            $validation = $this->skyDropXService->validateAddress($address);

            return response()->json([
                'success' => true,
                'data' => $validation,
            ]);
        } catch (SkyDropXException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
